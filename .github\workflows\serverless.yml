name: Deploy with Serverless
run-name: ${{ github.actor }} trigger serverless deployment 🚀

on:
   push:
      branches:
         - v2.1

jobs:
   deploy:
      runs-on: ubuntu-latest
      steps:
         -  name: Checkout repository
            uses: actions/checkout@v4

         -  name: Setup Node.js
            uses: actions/setup-node@v4
            with:
               node-version: '22'  # or your project's version

         -  name: Install dependencies
            run: npm ci

         -  name: Set Node.js memory limit
            run: export NODE_OPTIONS="--max-old-space-size=4096"

         -  name: build
            run: npm run build:lambda
            env:
               NODE_OPTIONS: "--max-old-space-size=4096"

         -  name: Configure AWS credentials
            uses: aws-actions/configure-aws-credentials@v2
            with:
               aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
               aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
               aws-region: eu-west-1

         -  name: Deploy with Serverless
            env:
               SERVERLESS_ACCESS_KEY: ${{ secrets.SERVERLESS_ACCESS_KEY }}
               # or if using AWS credentials directly
               AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
               AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            run: npm run deploy

         -  name: Extract environment variables from serverless.yml
            id: extract-env
            run: |
               # Extract APP_URL and CACHE_TOKEN from serverless.yml
               APP_URL=$(grep -A 20 "environment:" serverless.yml | grep "APP_URL:" | sed 's/.*APP_URL: *//' | tr -d ' ')
               CACHE_TOKEN=$(grep -A 20 "environment:" serverless.yml | grep "CACHE_TOKEN:" | sed 's/.*CACHE_TOKEN: *//' | tr -d ' ')
               echo "app_url=$APP_URL" >> $GITHUB_OUTPUT
               echo "cache_token=$CACHE_TOKEN" >> $GITHUB_OUTPUT

         -  name: Clear cache after deployment
            run: |
               curl --location '${{ steps.extract-env.outputs.app_url }}/api/cache/clear' \
               --request POST \
               --header 'Content-Type: application/json' \
               --header 'x-nuxt-multi-cache-token: ${{ steps.extract-env.outputs.cache_token }}'
      
      
